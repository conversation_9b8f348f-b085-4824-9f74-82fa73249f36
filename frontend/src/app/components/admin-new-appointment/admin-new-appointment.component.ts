import { Component, OnInit, ChangeDetectorRef, Inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormBuilder, FormGroup, Validators, FormsModule } from '@angular/forms';
import { RouterModule, Router, ActivatedRoute } from '@angular/router';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatSelectModule } from '@angular/material/select';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { MatDialog, MatDialogModule, MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { MatListModule } from '@angular/material/list';
import { MatChipsModule } from '@angular/material/chips';
import { MatDividerModule } from '@angular/material/divider';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { AdminService } from '../../services/admin.service';
import { AppointmentService } from '../../services/appointment.service';
import { AnalysisType, AppointmentCreate, Appointment, AppointmentStatus } from '../../models/appointment.model';
import { User } from '../../models/user.model';
import { SimpleLocationPickerComponent, SimpleLocation } from '../simple-location-picker/simple-location-picker.component';

// Interface pour les données du modal
export interface NurseSelectionData {
  appointment: Appointment;
  availableNurses: User[];
}

// Composant modal de test simple
@Component({
  selector: 'app-test-modal',
  standalone: true,
  imports: [CommonModule, MatDialogModule, MatButtonModule],
  template: `
    <h2 mat-dialog-title>Test Modal</h2>
    <mat-dialog-content>
      <p>{{ data.message }}</p>
      <p>Ce modal de test fonctionne !</p>
    </mat-dialog-content>
    <mat-dialog-actions>
      <button mat-button (click)="close()">Fermer</button>
    </mat-dialog-actions>
  `
})
export class TestModalComponent {
  constructor(
    public dialogRef: MatDialogRef<TestModalComponent>,
    @Inject(MAT_DIALOG_DATA) public data: any
  ) {}

  close(): void {
    this.dialogRef.close('test-result');
  }
}

// Composant modal pour la sélection d'infirmier
@Component({
  selector: 'app-nurse-selection-modal',
  standalone: true,
  imports: [
    CommonModule,
    MatDialogModule,
    MatButtonModule,
    MatListModule,
    MatIconModule,
    MatProgressSpinnerModule,
    MatChipsModule,
    MatDividerModule
  ],
  template: `
    <h2 mat-dialog-title>
      <mat-icon>person_add</mat-icon>
      Sélectionner un infirmier
    </h2>

    <mat-dialog-content>
      <div class="appointment-info">
        <h3>Rendez-vous</h3>
        <div class="info-row">
          <strong>Patient:</strong>
          {{ data.appointment.patient.firstName || 'N/A' }} {{ data.appointment.patient.lastName || 'N/A' }}
        </div>
        <div class="info-row">
          <strong>Date:</strong>
          {{ data.appointment.scheduledDate | date:'dd/MM/yyyy HH:mm' }}
        </div>
        <div class="info-row">
          <strong>Adresse:</strong>
          {{ data.appointment.homeAddress }}
        </div>
        <div class="info-row" *ngIf="data.appointment.analysisTypes && data.appointment.analysisTypes.length > 0">
          <strong>Analyses:</strong>
          <mat-chip-set>
            <mat-chip *ngFor="let analysis of data.appointment.analysisTypes">
              {{ analysis.name }}
            </mat-chip>
          </mat-chip-set>
        </div>
      </div>

      <mat-divider></mat-divider>

      <div class="nurses-section">
        <h3>Infirmiers disponibles ({{ data.availableNurses.length }})</h3>

        <mat-selection-list #nurseList [multiple]="false">
          <mat-list-option
            *ngFor="let nurse of data.availableNurses"
            [value]="nurse"
            class="nurse-option">
            <div class="nurse-info">
              <div class="nurse-header">
                <mat-icon matListItemIcon>local_hospital</mat-icon>
                <div class="nurse-name">
                  <strong>{{ nurse.firstName }} {{ nurse.lastName }}</strong>
                  <small>{{ nurse.email }}</small>
                </div>
              </div>

              <div class="nurse-details">
                <div class="detail-item" *ngIf="nurse.phone">
                  <mat-icon>phone</mat-icon>
                  <span>{{ nurse.phone }}</span>
                </div>

                <div class="detail-item" *ngIf="nurse.address">
                  <mat-icon>location_on</mat-icon>
                  <span>{{ nurse.address }}</span>
                </div>

                <div class="detail-item">
                  <mat-icon>schedule</mat-icon>
                  <span>Disponible</span>
                </div>
              </div>
            </div>
          </mat-list-option>
        </mat-selection-list>

        <div *ngIf="data.availableNurses.length === 0" class="no-nurses">
          <mat-icon>warning</mat-icon>
          <p>Aucun infirmier disponible pour le moment.</p>
        </div>
      </div>
    </mat-dialog-content>

    <mat-dialog-actions align="end">
      <button mat-button (click)="onCancel()">
        Annuler
      </button>

      <button mat-button (click)="onAutoAssign()" color="accent">
        <mat-icon>auto_fix_high</mat-icon>
        Affectation automatique
      </button>

      <button
        mat-raised-button
        color="primary"
        (click)="onAssignWithSelection(nurseList)"
        [disabled]="!nurseList.selectedOptions.hasValue()">
        <mat-icon>check</mat-icon>
        Affecter l'infirmier sélectionné
      </button>
    </mat-dialog-actions>
  `,
  styles: [`
    .appointment-info {
      background: #f5f5f5;
      padding: 16px;
      border-radius: 8px;
      margin-bottom: 20px;
    }

    .appointment-info h3 {
      margin: 0 0 12px 0;
      color: #1976d2;
    }

    .info-row {
      margin-bottom: 8px;
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .info-row strong {
      min-width: 80px;
    }

    .nurses-section h3 {
      margin: 16px 0 12px 0;
      color: #1976d2;
    }

    .nurse-option {
      border: 1px solid #e0e0e0;
      border-radius: 8px;
      margin-bottom: 12px;
      padding: 8px;
    }

    .nurse-option:hover {
      background-color: #f5f5f5;
    }

    .nurse-option.mat-mdc-list-option-selected {
      background-color: #e3f2fd;
      border-color: #1976d2;
    }

    .nurse-info {
      width: 100%;
    }

    .nurse-header {
      display: flex;
      align-items: center;
      gap: 12px;
      margin-bottom: 8px;
    }

    .nurse-name {
      display: flex;
      flex-direction: column;
    }

    .nurse-name small {
      color: #666;
      font-size: 12px;
    }

    .nurse-details {
      display: flex;
      flex-direction: column;
      gap: 4px;
      margin-left: 36px;
    }

    .detail-item {
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 14px;
      color: #666;
    }

    .detail-item mat-icon {
      font-size: 16px;
      width: 16px;
      height: 16px;
    }

    .no-nurses {
      text-align: center;
      padding: 40px;
      color: #666;
    }

    .no-nurses mat-icon {
      font-size: 48px;
      width: 48px;
      height: 48px;
      color: #ff9800;
      margin-bottom: 16px;
    }

    mat-dialog-content {
      max-height: 70vh;
      overflow-y: auto;
    }

    mat-dialog-actions {
      padding: 16px 24px;
      gap: 8px;
    }

    @media (max-width: 600px) {
      .nurse-details {
        margin-left: 0;
        margin-top: 8px;
      }

      .nurse-header {
        flex-direction: column;
        align-items: flex-start;
      }

      mat-dialog-actions {
        flex-direction: column;
      }

      mat-dialog-actions button {
        width: 100%;
        margin: 4px 0;
      }
    }
  `]
})
export class NurseSelectionModalComponent {
  constructor(
    public dialogRef: MatDialogRef<NurseSelectionModalComponent>,
    @Inject(MAT_DIALOG_DATA) public data: NurseSelectionData
  ) {
    // Trier les infirmiers par nom
    this.data.availableNurses.sort((a, b) =>
      `${a.firstName} ${a.lastName}`.localeCompare(`${b.firstName} ${b.lastName}`)
    );
  }

  onCancel(): void {
    this.dialogRef.close();
  }

  onAutoAssign(): void {
    this.dialogRef.close({ action: 'auto-assign' });
  }

  onAssignWithSelection(nurseList: any): void {
    const selectedOptions = nurseList.selectedOptions.selected;
    if (selectedOptions.length > 0) {
      const selectedNurse = selectedOptions[0].value;
      this.dialogRef.close({
        action: 'manual-assign',
        nurse: selectedNurse
      });
    }
  }
}

@Component({
  selector: 'app-admin-new-appointment',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    FormsModule,
    RouterModule,
    MatCardModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatIconModule,
    MatSelectModule,
    MatCheckboxModule,
    MatDatepickerModule,
    MatNativeDateModule,
    MatSnackBarModule,
    MatDialogModule,
    MatListModule,
    MatChipsModule,
    MatDividerModule,
    MatProgressSpinnerModule,
    SimpleLocationPickerComponent
  ],
  template: `
    <div class="admin-new-appointment-container">
      <mat-card class="appointment-card">
        <mat-card-header>
          <div class="header-content">
            <div class="title-section">
              <h2 class="card-title">
                <span class="custom-icon">👨‍💼</span>
                Nouveau rendez-vous
              </h2>

            </div>
            <button mat-icon-button routerLink="/dashboard/admin" class="close-btn">
              <mat-icon>close</mat-icon>
            </button>
          </div>
        </mat-card-header>

        <mat-card-content>
          <!-- Carte du patient -->
          <div class="patient-info-card" *ngIf="selectedPatient">
            <div class="patient-card-header">
              <h3 class="patient-card-title">
                <span class="custom-icon">👤</span>
                Informations du patient
              </h3>
            </div>
            <div class="patient-card-content">
              <div class="patient-main-info">
                <div class="patient-avatar">
                  {{ selectedPatient.firstName.charAt(0) }}{{ selectedPatient.lastName.charAt(0) }}
                </div>
                <div class="patient-details">
                  <div class="patient-name">{{ selectedPatient.firstName }} {{ selectedPatient.lastName }}</div>
                  <div class="patient-email">{{ selectedPatient.email }}</div>
                  <div class="patient-phone" *ngIf="selectedPatient.phone">{{ selectedPatient.phone }}</div>
                </div>
              </div>
              <div class="patient-additional-info">
                <div class="info-item" *ngIf="selectedPatient.address">
                  <span class="info-label">📍 Adresse :</span>
                  <span class="info-value">{{ selectedPatient.address }}</span>
                </div>
                
              </div>
            </div>
          </div>
          <form [formGroup]="appointmentForm" (ngSubmit)="onSubmit()" class="appointment-form">
            
            <!-- Section 1: Informations de base -->
            <div class="form-section">
              <h3 class="section-title">
                <span class="custom-icon">⏰</span>
                Date et heure du rendez-vous
              </h3>

              <mat-form-field appearance="outline" class="full-width">
                <mat-label>Date et heure souhaitées</mat-label>
                <input matInput [matDatepicker]="picker" formControlName="scheduledDate" required>
                <mat-datepicker-toggle matSuffix [for]="picker"></mat-datepicker-toggle>
                <mat-datepicker #picker></mat-datepicker>
                <mat-hint>Sélectionnez une date et heure pour le patient</mat-hint>
                <mat-error *ngIf="appointmentForm.get('scheduledDate')?.hasError('required')">
                  La date est requise
                </mat-error>
              </mat-form-field>
            </div>

            <!-- Section 2: Localisation -->
            <div class="form-section">
              <h3 class="section-title">
                <span class="custom-icon">📍</span>
                Où se déroule le rendez-vous ?
              </h3>

              <!-- Sélecteur de position sur carte -->
              <div class="location-picker-container">
                <app-simple-location-picker
                  (locationSelected)="onLocationSelected($event)"
                  [initialLocation]="selectedMapLocation">
                </app-simple-location-picker>
              </div>

              <!-- Adresse manuelle si pas de position sélectionnée -->
              <mat-form-field appearance="outline" class="full-width" *ngIf="!selectedMapLocation">
                <mat-label>Ou saisissez l'adresse manuellement</mat-label>
                <textarea matInput formControlName="homeAddress" rows="3" required
                          placeholder="Numéro, rue, ville, code postal..."></textarea>
                <span class="custom-icon" matSuffix>✏️</span>
                <mat-hint>Vous pouvez aussi utiliser la carte ci-dessus</mat-hint>
                <mat-error *ngIf="appointmentForm.get('homeAddress')?.hasError('required')">
                  L'adresse est requise
                </mat-error>
              </mat-form-field>
            </div>

            <!-- Section 3: Types d'analyses -->
            <div class="form-section">
              <h3 class="section-title">
                <span class="custom-icon">🧪</span>
                Analyses demandées
              </h3>

              <mat-form-field appearance="outline" class="full-width analysis-select">
                <mat-label>Sélectionnez les analyses</mat-label>
                <mat-select formControlName="analysisTypeIds" multiple required>
                  <mat-option *ngFor="let analysis of analysisTypes" [value]="analysis.id" class="analysis-option">
                    <div class="analysis-item">
                      <div class="analysis-main">
                        <span class="analysis-name">{{analysis.name}}</span>
                        <span class="analysis-price">{{analysis.price}}€</span>
                      </div>
                      <div class="analysis-details">
                        <span class="analysis-duration">
                          <span class="custom-icon">⏱️</span>
                          {{analysis.durationMinutes}} min
                        </span>
                        <span class="analysis-prep" *ngIf="analysis.preparationRequired">
                          <span class="custom-icon">⚠️</span>
                          Préparation requise
                        </span>
                      </div>
                    </div>
                  </mat-option>
                </mat-select>
                <mat-error *ngIf="appointmentForm.get('analysisTypeIds')?.hasError('required')">
                  Au moins une analyse est requise
                </mat-error>
              </mat-form-field>

              <!-- Prix estimé -->
              <div class="price-summary" *ngIf="estimatedPrice > 0">
                <span class="custom-icon">💰</span>
                <span class="price-label">Prix estimé :</span>
                <span class="price-value">{{ estimatedPrice }}€</span>
              </div>
            </div>

            <!-- Section 4: Informations complémentaires -->
            <div class="form-section">
              <h3 class="section-title">
                <span class="custom-icon">📝</span>
                Informations complémentaires
              </h3>

              <mat-form-field appearance="outline" class="full-width">
                <mat-label>Symptômes (optionnel)</mat-label>
                <textarea matInput formControlName="symptoms" rows="3"
                          placeholder="Décrivez les symptômes du patient..."></textarea>
                <span class="custom-icon" matSuffix>🩹</span>
                <mat-hint>Ces informations aideront notre équipe médicale</mat-hint>
              </mat-form-field>

              <mat-form-field appearance="outline" class="full-width">
                <mat-label>Instructions spéciales (optionnel)</mat-label>
                <textarea matInput formControlName="specialInstructions" rows="3"
                          placeholder="Instructions particulières pour l'infirmier..."></textarea>
                <span class="custom-icon" matSuffix>📋</span>
                <mat-hint>Informations importantes pour le prélèvement</mat-hint>
              </mat-form-field>

              <div class="checkbox-section">
                <mat-checkbox formControlName="isUrgent" class="urgent-checkbox">
                  <span class="custom-icon">🚨</span>
                  Rendez-vous urgent
                </mat-checkbox>
                <p class="checkbox-hint">
                  Cochez cette case si le rendez-vous nécessite une prise en charge prioritaire
                </p>
              </div>
            </div>

            <!-- Section 5: Affectation d'infirmier -->
            <div class="form-section">
              <h3 class="section-title">
                <span class="custom-icon">👩‍⚕️</span>
                Affectation d'infirmier (optionnel)
              </h3>

              <div class="nurse-assignment-options">
                <div class="assignment-choice">
                  <input type="radio" id="no-assignment" name="nurseAssignment" value="none"
                         [(ngModel)]="nurseAssignmentChoice" class="radio-input">
                  <label for="no-assignment" class="radio-label">
                    <span class="custom-icon">⏳</span>
                    Assigner plus tard
                  </label>
                  <p class="choice-description">Le rendez-vous sera créé en attente d'affectation</p>
                </div>

                <div class="assignment-choice">
                  <input type="radio" id="auto-assignment" name="nurseAssignment" value="auto"
                         [(ngModel)]="nurseAssignmentChoice" class="radio-input">
                  <label for="auto-assignment" class="radio-label">
                    <span class="custom-icon">🤖</span>
                    Affectation automatique
                  </label>
                  <p class="choice-description">L'infirmier le plus proche sera automatiquement assigné</p>
                </div>

                <div class="assignment-choice">
                  <input type="radio" id="manual-assignment" name="nurseAssignment" value="manual"
                         [(ngModel)]="nurseAssignmentChoice" class="radio-input">
                  <label for="manual-assignment" class="radio-label">
                    <span class="custom-icon">👥</span>
                    Choisir un infirmier
                  </label>
                  <p class="choice-description">Sélectionner manuellement un infirmier disponible</p>
                </div>
              </div>

              <!-- Bouton pour ouvrir le modal de sélection -->
              <div class="nurse-selection-button" *ngIf="nurseAssignmentChoice === 'manual'">
                <button type="button"
                        class="select-nurse-btn"
                        (click)="openNurseSelectionModal()"
                        [disabled]="!availableNurses.length">
                  <span class="custom-icon">👩‍⚕️</span>
                  {{ selectedNurse ? 'Changer d\'infirmier' : 'Sélectionner un infirmier' }}
                  <span class="btn-count" *ngIf="availableNurses.length">({{ availableNurses.length }} disponibles)</span>
                </button>

                <!-- Debug info -->
                <div class="debug-info" style="margin-top: 10px; padding: 10px; background: #f0f0f0; border-radius: 4px; font-size: 12px;">
                  <strong>Debug:</strong><br>
                  - Infirmiers chargés: {{ availableNurses.length }}<br>
                  - Patient sélectionné: {{ selectedPatient ? 'Oui' : 'Non' }}<br>
                  - Choice: {{ nurseAssignmentChoice }}<br>
                  <button type="button" (click)="testSimpleModal()" style="margin-top: 5px; padding: 5px 10px; background: #ff9800; color: white; border: none; border-radius: 3px;">
                    Test Modal Simple
                  </button>
                </div>

                <!-- Résumé de l'infirmier sélectionné -->
                <div class="selected-nurse-summary" *ngIf="selectedNurse">
                  <div class="summary-header">
                    <span class="custom-icon">✅</span>
                    Infirmier sélectionné
                  </div>
                  <div class="summary-content">
                    <div class="nurse-avatar-small">
                      {{ selectedNurse.firstName.charAt(0) }}{{ selectedNurse.lastName.charAt(0) }}
                    </div>
                    <div class="nurse-details">
                      <strong>{{ selectedNurse.firstName }} {{ selectedNurse.lastName }}</strong>
                      <span class="summary-email">{{ selectedNurse.email }}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Actions -->
            <div class="form-actions">
              <button mat-stroked-button type="button" routerLink="/dashboard/admin" class="cancel-btn">
                <span class="custom-icon">⬅️</span>
                Retour
              </button>
              <button mat-raised-button color="primary" type="submit"
                      [disabled]="appointmentForm.invalid || isLoading" class="submit-btn">
                <span class="custom-icon" *ngIf="!isLoading">✅</span>
                <span class="custom-icon spinning" *ngIf="isLoading">⏳</span>
                <span *ngIf="!isLoading">Créer le rendez-vous</span>
                <span *ngIf="isLoading">Création en cours...</span>
              </button>
            </div>
          </form>
        </mat-card-content>
      </mat-card>
    </div>
  `,
  styles: [`
    .admin-new-appointment-container {
      padding: 20px;
      max-width: 900px;
      margin: 0 auto;
      background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
      min-height: 100vh;
    }

    .appointment-card {
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
      border-radius: 16px;
      overflow: hidden;
    }

    .header-content {
      display: flex;
      justify-content: space-between;
      align-items: center;
      width: 100%;
    }

    .title-section {
      flex: 1;
    }

    .card-title {
      margin: 0;
      font-size: 1.8rem;
      font-weight: 600;
      color: #2c3e50;
      display: flex;
      align-items: center;
      gap: 12px;
    }

    .card-subtitle {
      margin: 8px 0 0 0;
      color: #7f8c8d;
      font-size: 1rem;
    }

    .close-btn {
      color: #95a5a6;
    }

    .appointment-form {
      display: flex;
      flex-direction: column;
      gap: 32px;
    }

    .form-section {
      background: #f8f9fa;
      padding: 24px;
      border-radius: 12px;
      border-left: 4px solid #3498db;
    }

    .section-title {
      margin: 0 0 20px 0;
      font-size: 1.3rem;
      font-weight: 600;
      color: #2c3e50;
      display: flex;
      align-items: center;
      gap: 12px;
    }

    .full-width {
      width: 100%;
      margin-bottom: 16px;
    }

    .location-picker-container {
      margin-bottom: 20px;
      border-radius: 8px;
      overflow: hidden;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .analysis-select .mat-mdc-select-panel {
      max-height: 400px;
    }

    .analysis-item {
      padding: 8px 0;
      width: 100%;
    }

    .analysis-main {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 4px;
    }

    .analysis-name {
      font-weight: 500;
      color: #2c3e50;
    }

    .analysis-price {
      font-weight: 600;
      color: #27ae60;
    }

    .analysis-details {
      display: flex;
      gap: 16px;
      font-size: 0.85rem;
      color: #7f8c8d;
    }

    .analysis-duration,
    .analysis-prep {
      display: flex;
      align-items: center;
      gap: 4px;
    }

    .price-summary {
      background: #e8f5e8;
      padding: 16px;
      border-radius: 8px;
      display: flex;
      align-items: center;
      gap: 12px;
      margin-top: 16px;
    }

    .price-label {
      font-weight: 500;
      color: #2c3e50;
    }

    .price-value {
      font-size: 1.2rem;
      font-weight: 700;
      color: #27ae60;
    }

    .checkbox-section {
      margin-top: 16px;
    }

    .urgent-checkbox {
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .checkbox-hint {
      margin: 8px 0 0 32px;
      font-size: 0.9rem;
      color: #7f8c8d;
    }

    .form-actions {
      display: flex;
      gap: 16px;
      justify-content: flex-end;
      padding-top: 24px;
      border-top: 1px solid #ecf0f1;
    }

    .cancel-btn {
      min-width: 120px;
    }

    .submit-btn {
      min-width: 180px;
    }

    .custom-icon {
      font-size: 1.2em;
    }

    .spinning {
      animation: spin 1s linear infinite;
    }

    @keyframes spin {
      from { transform: rotate(0deg); }
      to { transform: rotate(360deg); }
    }

    /* Styles pour la carte du patient */
    .patient-info-card {
      background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
      border-radius: 12px;
      padding: 20px;
      margin-bottom: 24px;
      border-left: 4px solid #2196f3;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .patient-card-header {
      margin-bottom: 16px;
    }

    .patient-card-title {
      margin: 0;
      font-size: 1.2rem;
      font-weight: 600;
      color: #1976d2;
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .patient-card-content {
      display: flex;
      flex-direction: column;
      gap: 16px;
    }

    .patient-main-info {
      display: flex;
      align-items: center;
      gap: 16px;
    }

    .patient-avatar {
      width: 60px;
      height: 60px;
      border-radius: 50%;
      background: linear-gradient(135deg, #2196f3, #21cbf3);
      color: white;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: 600;
      font-size: 1.2rem;
      text-transform: uppercase;
    }

    .patient-details {
      flex: 1;
    }

    .patient-name {
      font-size: 1.3rem;
      font-weight: 600;
      color: #1976d2;
      margin-bottom: 4px;
    }

    .patient-email {
      color: #666;
      font-size: 0.95rem;
      margin-bottom: 2px;
    }

    .patient-phone {
      color: #666;
      font-size: 0.9rem;
    }

    .patient-additional-info {
      display: flex;
      flex-wrap: wrap;
      gap: 16px;
    }

    .info-item {
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 0.9rem;
    }

    .info-label {
      font-weight: 500;
      color: #555;
    }

    .info-value {
      color: #333;
    }

    /* Styles pour l'affectation d'infirmier */
    .nurse-assignment-options {
      display: flex;
      flex-direction: column;
      gap: 16px;
      margin-bottom: 20px;
    }

    .assignment-choice {
      border: 2px solid #e0e0e0;
      border-radius: 8px;
      padding: 16px;
      transition: all 0.3s ease;
      cursor: pointer;
    }

    .assignment-choice:hover {
      border-color: #2196f3;
      background-color: #f8f9fa;
    }

    .assignment-choice:has(.radio-input:checked) {
      border-color: #2196f3;
      background-color: #e3f2fd;
    }

    .radio-input {
      margin-right: 12px;
      transform: scale(1.2);
    }

    .radio-label {
      display: flex;
      align-items: center;
      gap: 8px;
      font-weight: 500;
      color: #333;
      cursor: pointer;
      margin-bottom: 8px;
    }

    .choice-description {
      margin: 0;
      color: #666;
      font-size: 0.9rem;
      margin-left: 32px;
    }

    .nurse-selection-button {
      margin-top: 20px;
    }

    .select-nurse-btn {
      width: 100%;
      padding: 16px 20px;
      background: linear-gradient(135deg, #2196f3, #21cbf3);
      color: white;
      border: none;
      border-radius: 8px;
      font-size: 1rem;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.3s ease;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 8px;
      margin-bottom: 16px;
    }

    .select-nurse-btn:hover:not(:disabled) {
      background: linear-gradient(135deg, #1976d2, #1e88e5);
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(33, 150, 243, 0.3);
    }

    .select-nurse-btn:disabled {
      background: #e0e0e0;
      color: #999;
      cursor: not-allowed;
      transform: none;
      box-shadow: none;
    }

    .btn-count {
      font-size: 0.9rem;
      opacity: 0.9;
    }

    .selected-nurse-summary {
      padding: 16px;
      background: #e8f5e9;
      border-radius: 8px;
      border-left: 4px solid #4caf50;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .summary-header {
      display: flex;
      align-items: center;
      gap: 8px;
      font-weight: 600;
      color: #2e7d32;
      margin-bottom: 12px;
    }

    .summary-content {
      display: flex;
      align-items: center;
      gap: 12px;
    }

    .nurse-avatar-small {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      background: linear-gradient(135deg, #4caf50, #8bc34a);
      color: white;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: 600;
      font-size: 0.9rem;
      text-transform: uppercase;
    }

    .nurse-details {
      flex: 1;
      display: flex;
      flex-direction: column;
      gap: 4px;
    }

    .summary-email {
      font-size: 0.9rem;
      color: #666;
    }

    @media (max-width: 768px) {
      .admin-new-appointment-container {
        padding: 10px;
      }

      .form-section {
        padding: 16px;
      }

      .form-actions {
        flex-direction: column;
      }

      .cancel-btn,
      .submit-btn {
        width: 100%;
      }

      .patient-main-info {
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;
      }

      .patient-additional-info {
        flex-direction: column;
        gap: 8px;
      }

      .assignment-choice {
        padding: 12px;
      }

      .nurse-item {
        padding: 10px 12px;
      }
    }
  `]
})
export class AdminNewAppointmentComponent implements OnInit {
  appointmentForm: FormGroup;
  analysisTypes: AnalysisType[] = [];
  selectedPatient: User | null = null;
  patientId: number | null = null;
  isLoading = false;
  estimatedPrice = 0;
  selectedMapLocation: SimpleLocation | undefined = undefined;

  // Propriétés pour l'affectation d'infirmier
  nurseAssignmentChoice: 'none' | 'auto' | 'manual' = 'none';
  availableNurses: User[] = [];
  selectedNurse: User | null = null;

  constructor(
    private fb: FormBuilder,
    private adminService: AdminService,
    private appointmentService: AppointmentService,
    private router: Router,
    private route: ActivatedRoute,
    private snackBar: MatSnackBar,
    private cdr: ChangeDetectorRef,
    private dialog: MatDialog
  ) {
    this.appointmentForm = this.fb.group({
      scheduledDate: ['', Validators.required],
      homeAddress: ['', Validators.required],
      analysisTypeIds: [[], Validators.required],
      symptoms: [''],
      specialInstructions: [''],
      isUrgent: [false]
    });

    // Calculate price when analysis types change
    this.appointmentForm.get('analysisTypeIds')?.valueChanges.subscribe(ids => {
      this.calculateEstimatedPrice(ids);
    });
  }

  ngOnInit(): void {
    // Récupérer l'ID du patient depuis les paramètres de route
    this.route.params.subscribe(params => {
      this.patientId = +params['patientId'];
      if (this.patientId) {
        this.loadPatientInfo();
      }
    });

    this.loadAnalysisTypes();
    this.loadAvailableNurses();
  }

  loadPatientInfo(): void {
    if (!this.patientId) return;

    this.adminService.getPatientById(this.patientId).subscribe({
      next: (patient) => {
        this.selectedPatient = patient;
        this.cdr.detectChanges();
      },
      error: (error) => {
        console.error('Erreur chargement patient:', error);
        this.snackBar.open('Erreur lors du chargement des informations du patient', 'Fermer', {
          duration: 5000,
          panelClass: ['error-snackbar']
        });
        this.router.navigate(['/dashboard/admin']);
      }
    });
  }

  loadAnalysisTypes(): void {
    this.appointmentService.getAnalysisTypes().subscribe({
      next: (types) => {
        this.analysisTypes = types.filter(type => type.isActive);
        this.cdr.detectChanges();
      },
      error: (error) => {
        console.error('Error loading analysis types:', error);
        this.snackBar.open('Erreur lors du chargement des types d\'analyses', 'Fermer', {
          duration: 5000,
          panelClass: ['error-snackbar']
        });
      }
    });
  }

  calculateEstimatedPrice(analysisTypeIds: number[]): void {
    if (!analysisTypeIds || analysisTypeIds.length === 0) {
      this.estimatedPrice = 0;
      return;
    }

    this.estimatedPrice = this.analysisTypes
      .filter(type => analysisTypeIds.includes(type.id))
      .reduce((total, type) => total + type.price, 0);
  }

  onLocationSelected(location: SimpleLocation): void {
    this.selectedMapLocation = location;
    if (location) {
      this.appointmentForm.patchValue({
        homeAddress: location.address
      });
    }
  }

  onSubmit(): void {
    if (this.appointmentForm.valid && this.patientId) {
      // Vérifier qu'une position a été sélectionnée
      if (!this.selectedMapLocation) {
        this.snackBar.open(
          '⚠️ Veuillez sélectionner la position sur la carte avant de continuer',
          'Fermer',
          { duration: 5000, panelClass: ['warning-snackbar'] }
        );
        return;
      }

      this.isLoading = true;

      const formValue = this.appointmentForm.value;
      const appointmentData: AppointmentCreate = {
        scheduledDate: formValue.scheduledDate,
        homeAddress: formValue.homeAddress,
        analysisTypeIds: formValue.analysisTypeIds,
        symptoms: formValue.symptoms || undefined,
        specialInstructions: formValue.specialInstructions || undefined,
        isUrgent: formValue.isUrgent || false,
        latitude: this.selectedMapLocation?.latitude,
        longitude: this.selectedMapLocation?.longitude
      };

      this.adminService.createAppointmentForPatient(this.patientId, appointmentData).subscribe({
        next: (appointment) => {
          this.isLoading = false;
          this.cdr.detectChanges();

          // Gérer l'affectation d'infirmier si nécessaire
          if (this.nurseAssignmentChoice !== 'none') {
            this.handleNurseAssignment(appointment.id);
          }

          this.snackBar.open('Rendez-vous créé avec succès!', 'Fermer', {
            duration: 3000,
            panelClass: ['success-snackbar']
          });
          this.router.navigate(['/dashboard/admin']);
        },
        error: (error) => {
          this.isLoading = false;
          this.cdr.detectChanges();
          console.error('Error creating appointment:', error);
          this.snackBar.open('Erreur lors de la création du rendez-vous', 'Fermer', {
            duration: 5000,
            panelClass: ['error-snackbar']
          });
        }
      });
    }
  }

  testSimpleModal(): void {
    console.log('🧪 TEST - Ouverture d\'un modal simple');
    try {
      const dialogRef = this.dialog.open(TestModalComponent, {
        width: '400px',
        data: { message: 'Test modal simple' }
      });

      console.log('✅ Modal simple ouvert:', dialogRef);

      dialogRef.afterClosed().subscribe(result => {
        console.log('Modal simple fermé:', result);
      });
    } catch (error) {
      console.error('❌ Erreur modal simple:', error);
    }
  }

  loadAvailableNurses(): void {
    this.adminService.getAvailableNurses().subscribe({
      next: (nurses) => {
        this.availableNurses = nurses;
        this.cdr.detectChanges();
      },
      error: (error) => {
        console.error('Erreur chargement infirmiers:', error);
        this.snackBar.open('Erreur lors du chargement des infirmiers', 'Fermer', {
          duration: 5000,
          panelClass: ['error-snackbar']
        });
      }
    });
  }

  openNurseSelectionModal(): void {
    try {
      console.log('🔍 DÉBUT - Ouverture du modal de sélection d\'infirmier');
      console.log('Infirmiers disponibles:', this.availableNurses);
      console.log('Patient sélectionné:', this.selectedPatient);
      console.log('Dialog service:', this.dialog);

      // Vérifications préalables
      if (!this.selectedPatient) {
        console.error('❌ Aucun patient sélectionné');
        this.snackBar.open('Erreur: Aucun patient sélectionné', 'Fermer', {
          duration: 3000,
          panelClass: ['error-snackbar']
        });
        return;
      }

      if (!this.availableNurses || this.availableNurses.length === 0) {
        console.error('❌ Aucun infirmier disponible');
        this.snackBar.open('Aucun infirmier disponible pour le moment', 'Fermer', {
          duration: 3000,
          panelClass: ['warning-snackbar']
        });
        return;
      }

      console.log('✅ Vérifications passées, création du rendez-vous temporaire...');

    // Créer un rendez-vous temporaire pour le modal
    const tempAppointment: Appointment = {
      id: 0, // ID temporaire
      patient: this.selectedPatient!,
      analysisTypes: this.analysisTypes.filter(type =>
        this.appointmentForm.get('analysisTypeIds')?.value?.includes(type.id)
      ),
      scheduledDate: this.appointmentForm.get('scheduledDate')?.value,
      homeAddress: this.appointmentForm.get('homeAddress')?.value,
      latitude: this.selectedMapLocation?.latitude,
      longitude: this.selectedMapLocation?.longitude,
      status: AppointmentStatus.PENDING,
      symptoms: this.appointmentForm.get('symptoms')?.value,
      specialInstructions: this.appointmentForm.get('specialInstructions')?.value,
      totalPrice: this.estimatedPrice,
      isUrgent: this.appointmentForm.get('isUrgent')?.value || false,
      estimatedDurationMinutes: this.analysisTypes
        .filter(type => this.appointmentForm.get('analysisTypeIds')?.value?.includes(type.id))
        .reduce((total, type) => total + type.durationMinutes, 0),
      createdAt: new Date(),
      updatedAt: new Date()
    };

    const dialogData: NurseSelectionData = {
      appointment: tempAppointment,
      availableNurses: this.availableNurses
    };

    console.log('📋 Données du modal:', dialogData);

    const dialogRef = this.dialog.open(NurseSelectionModalComponent, {
      width: '800px',
      maxWidth: '90vw',
      data: dialogData,
      disableClose: false
    });

    console.log('🎭 Modal ouvert, référence:', dialogRef);

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        if (result.action === 'auto-assign') {
          this.nurseAssignmentChoice = 'auto';
          this.selectedNurse = null;
        } else if (result.action === 'manual-assign' && result.nurse) {
          this.nurseAssignmentChoice = 'manual';
          this.selectedNurse = result.nurse;
        }
        this.cdr.detectChanges();
      }
    });

    } catch (error) {
      console.error('💥 ERREUR lors de l\'ouverture du modal:', error);
      this.snackBar.open('Erreur technique lors de l\'ouverture du modal', 'Fermer', {
        duration: 5000,
        panelClass: ['error-snackbar']
      });
    }
  }

  private handleNurseAssignment(appointmentId: number): void {
    if (this.nurseAssignmentChoice === 'auto') {
      // Affectation automatique
      this.adminService.autoAssignNearestNurse(appointmentId).subscribe({
        next: () => {
          this.snackBar.open('Infirmier assigné automatiquement!', 'Fermer', {
            duration: 3000,
            panelClass: ['success-snackbar']
          });
        },
        error: (error) => {
          console.error('Erreur affectation automatique:', error);
          this.snackBar.open('Erreur lors de l\'affectation automatique', 'Fermer', {
            duration: 5000,
            panelClass: ['error-snackbar']
          });
        }
      });
    } else if (this.nurseAssignmentChoice === 'manual' && this.selectedNurse) {
      // Affectation manuelle
      this.adminService.assignNurseToAppointment(appointmentId, this.selectedNurse.id!).subscribe({
        next: () => {
          this.snackBar.open(`Infirmier ${this.selectedNurse!.firstName} ${this.selectedNurse!.lastName} assigné!`, 'Fermer', {
            duration: 3000,
            panelClass: ['success-snackbar']
          });
        },
        error: (error) => {
          console.error('Erreur affectation manuelle:', error);
          this.snackBar.open('Erreur lors de l\'affectation de l\'infirmier', 'Fermer', {
            duration: 5000,
            panelClass: ['error-snackbar']
          });
        }
      });
    }
  }
}
