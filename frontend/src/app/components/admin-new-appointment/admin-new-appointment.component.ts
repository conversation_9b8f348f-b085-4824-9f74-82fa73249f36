import { Component, OnInit, ChangeDetectorRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { RouterModule, Router, ActivatedRoute } from '@angular/router';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatSelectModule } from '@angular/material/select';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { AdminService } from '../../services/admin.service';
import { AppointmentService } from '../../services/appointment.service';
import { AnalysisType, AppointmentCreate } from '../../models/appointment.model';
import { User } from '../../models/user.model';
import { SimpleLocationPickerComponent, SimpleLocation } from '../simple-location-picker/simple-location-picker.component';

@Component({
  selector: 'app-admin-new-appointment',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    RouterModule,
    MatCardModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatIconModule,
    MatSelectModule,
    MatCheckboxModule,
    MatDatepickerModule,
    MatNativeDateModule,
    MatSnackBarModule,
    SimpleLocationPickerComponent
  ],
  template: `
    <div class="admin-new-appointment-container">
      <mat-card class="appointment-card">
        <mat-card-header>
          <div class="header-content">
            <div class="title-section">
              <h2 class="card-title">
                <span class="custom-icon">👨‍💼</span>
                Nouveau rendez-vous
              </h2>
              <p class="card-subtitle" *ngIf="selectedPatient">
                Pour le patient : {{ selectedPatient.firstName }} {{ selectedPatient.lastName }}
              </p>
            </div>
            <button mat-icon-button routerLink="/dashboard/admin" class="close-btn">
              <mat-icon>close</mat-icon>
            </button>
          </div>
        </mat-card-header>

        <mat-card-content>
          <form [formGroup]="appointmentForm" (ngSubmit)="onSubmit()" class="appointment-form">
            
            <!-- Section 1: Informations de base -->
            <div class="form-section">
              <h3 class="section-title">
                <span class="custom-icon">⏰</span>
                Date et heure du rendez-vous
              </h3>

              <mat-form-field appearance="outline" class="full-width">
                <mat-label>Date et heure souhaitées</mat-label>
                <input matInput [matDatepicker]="picker" formControlName="scheduledDate" required>
                <mat-datepicker-toggle matSuffix [for]="picker"></mat-datepicker-toggle>
                <mat-datepicker #picker></mat-datepicker>
                <mat-hint>Sélectionnez une date et heure pour le patient</mat-hint>
                <mat-error *ngIf="appointmentForm.get('scheduledDate')?.hasError('required')">
                  La date est requise
                </mat-error>
              </mat-form-field>
            </div>

            <!-- Section 2: Localisation -->
            <div class="form-section">
              <h3 class="section-title">
                <span class="custom-icon">📍</span>
                Où se déroule le rendez-vous ?
              </h3>

              <!-- Sélecteur de position sur carte -->
              <div class="location-picker-container">
                <app-simple-location-picker
                  (locationSelected)="onLocationSelected($event)"
                  [initialLocation]="selectedMapLocation">
                </app-simple-location-picker>
              </div>

              <!-- Adresse manuelle si pas de position sélectionnée -->
              <mat-form-field appearance="outline" class="full-width" *ngIf="!selectedMapLocation">
                <mat-label>Ou saisissez l'adresse manuellement</mat-label>
                <textarea matInput formControlName="homeAddress" rows="3" required
                          placeholder="Numéro, rue, ville, code postal..."></textarea>
                <span class="custom-icon" matSuffix>✏️</span>
                <mat-hint>Vous pouvez aussi utiliser la carte ci-dessus</mat-hint>
                <mat-error *ngIf="appointmentForm.get('homeAddress')?.hasError('required')">
                  L'adresse est requise
                </mat-error>
              </mat-form-field>
            </div>

            <!-- Section 3: Types d'analyses -->
            <div class="form-section">
              <h3 class="section-title">
                <span class="custom-icon">🧪</span>
                Analyses demandées
              </h3>

              <mat-form-field appearance="outline" class="full-width analysis-select">
                <mat-label>Sélectionnez les analyses</mat-label>
                <mat-select formControlName="analysisTypeIds" multiple required>
                  <mat-option *ngFor="let analysis of analysisTypes" [value]="analysis.id" class="analysis-option">
                    <div class="analysis-item">
                      <div class="analysis-main">
                        <span class="analysis-name">{{analysis.name}}</span>
                        <span class="analysis-price">{{analysis.price}}€</span>
                      </div>
                      <div class="analysis-details">
                        <span class="analysis-duration">
                          <span class="custom-icon">⏱️</span>
                          {{analysis.durationMinutes}} min
                        </span>
                        <span class="analysis-prep" *ngIf="analysis.preparationRequired">
                          <span class="custom-icon">⚠️</span>
                          Préparation requise
                        </span>
                      </div>
                    </div>
                  </mat-option>
                </mat-select>
                <mat-error *ngIf="appointmentForm.get('analysisTypeIds')?.hasError('required')">
                  Au moins une analyse est requise
                </mat-error>
              </mat-form-field>

              <!-- Prix estimé -->
              <div class="price-summary" *ngIf="estimatedPrice > 0">
                <span class="custom-icon">💰</span>
                <span class="price-label">Prix estimé :</span>
                <span class="price-value">{{ estimatedPrice }}€</span>
              </div>
            </div>

            <!-- Section 4: Informations complémentaires -->
            <div class="form-section">
              <h3 class="section-title">
                <span class="custom-icon">📝</span>
                Informations complémentaires
              </h3>

              <mat-form-field appearance="outline" class="full-width">
                <mat-label>Symptômes (optionnel)</mat-label>
                <textarea matInput formControlName="symptoms" rows="3"
                          placeholder="Décrivez les symptômes du patient..."></textarea>
                <span class="custom-icon" matSuffix>🩹</span>
                <mat-hint>Ces informations aideront notre équipe médicale</mat-hint>
              </mat-form-field>

              <mat-form-field appearance="outline" class="full-width">
                <mat-label>Instructions spéciales (optionnel)</mat-label>
                <textarea matInput formControlName="specialInstructions" rows="3"
                          placeholder="Instructions particulières pour l'infirmier..."></textarea>
                <span class="custom-icon" matSuffix>📋</span>
                <mat-hint>Informations importantes pour le prélèvement</mat-hint>
              </mat-form-field>

              <div class="checkbox-section">
                <mat-checkbox formControlName="isUrgent" class="urgent-checkbox">
                  <span class="custom-icon">🚨</span>
                  Rendez-vous urgent
                </mat-checkbox>
                <p class="checkbox-hint">
                  Cochez cette case si le rendez-vous nécessite une prise en charge prioritaire
                </p>
              </div>
            </div>

            <!-- Actions -->
            <div class="form-actions">
              <button mat-stroked-button type="button" routerLink="/dashboard/admin" class="cancel-btn">
                <span class="custom-icon">⬅️</span>
                Retour
              </button>
              <button mat-raised-button color="primary" type="submit"
                      [disabled]="appointmentForm.invalid || isLoading" class="submit-btn">
                <span class="custom-icon" *ngIf="!isLoading">✅</span>
                <span class="custom-icon spinning" *ngIf="isLoading">⏳</span>
                <span *ngIf="!isLoading">Créer le rendez-vous</span>
                <span *ngIf="isLoading">Création en cours...</span>
              </button>
            </div>
          </form>
        </mat-card-content>
      </mat-card>
    </div>
  `,
  styles: [`
    .admin-new-appointment-container {
      padding: 20px;
      max-width: 900px;
      margin: 0 auto;
      background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
      min-height: 100vh;
    }

    .appointment-card {
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
      border-radius: 16px;
      overflow: hidden;
    }

    .header-content {
      display: flex;
      justify-content: space-between;
      align-items: center;
      width: 100%;
    }

    .title-section {
      flex: 1;
    }

    .card-title {
      margin: 0;
      font-size: 1.8rem;
      font-weight: 600;
      color: #2c3e50;
      display: flex;
      align-items: center;
      gap: 12px;
    }

    .card-subtitle {
      margin: 8px 0 0 0;
      color: #7f8c8d;
      font-size: 1rem;
    }

    .close-btn {
      color: #95a5a6;
    }

    .appointment-form {
      display: flex;
      flex-direction: column;
      gap: 32px;
    }

    .form-section {
      background: #f8f9fa;
      padding: 24px;
      border-radius: 12px;
      border-left: 4px solid #3498db;
    }

    .section-title {
      margin: 0 0 20px 0;
      font-size: 1.3rem;
      font-weight: 600;
      color: #2c3e50;
      display: flex;
      align-items: center;
      gap: 12px;
    }

    .full-width {
      width: 100%;
      margin-bottom: 16px;
    }

    .location-picker-container {
      margin-bottom: 20px;
      border-radius: 8px;
      overflow: hidden;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .analysis-select .mat-mdc-select-panel {
      max-height: 400px;
    }

    .analysis-item {
      padding: 8px 0;
      width: 100%;
    }

    .analysis-main {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 4px;
    }

    .analysis-name {
      font-weight: 500;
      color: #2c3e50;
    }

    .analysis-price {
      font-weight: 600;
      color: #27ae60;
    }

    .analysis-details {
      display: flex;
      gap: 16px;
      font-size: 0.85rem;
      color: #7f8c8d;
    }

    .analysis-duration,
    .analysis-prep {
      display: flex;
      align-items: center;
      gap: 4px;
    }

    .price-summary {
      background: #e8f5e8;
      padding: 16px;
      border-radius: 8px;
      display: flex;
      align-items: center;
      gap: 12px;
      margin-top: 16px;
    }

    .price-label {
      font-weight: 500;
      color: #2c3e50;
    }

    .price-value {
      font-size: 1.2rem;
      font-weight: 700;
      color: #27ae60;
    }

    .checkbox-section {
      margin-top: 16px;
    }

    .urgent-checkbox {
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .checkbox-hint {
      margin: 8px 0 0 32px;
      font-size: 0.9rem;
      color: #7f8c8d;
    }

    .form-actions {
      display: flex;
      gap: 16px;
      justify-content: flex-end;
      padding-top: 24px;
      border-top: 1px solid #ecf0f1;
    }

    .cancel-btn {
      min-width: 120px;
    }

    .submit-btn {
      min-width: 180px;
    }

    .custom-icon {
      font-size: 1.2em;
    }

    .spinning {
      animation: spin 1s linear infinite;
    }

    @keyframes spin {
      from { transform: rotate(0deg); }
      to { transform: rotate(360deg); }
    }

    @media (max-width: 768px) {
      .admin-new-appointment-container {
        padding: 10px;
      }

      .form-section {
        padding: 16px;
      }

      .form-actions {
        flex-direction: column;
      }

      .cancel-btn,
      .submit-btn {
        width: 100%;
      }
    }
  `]
})
export class AdminNewAppointmentComponent implements OnInit {
  appointmentForm: FormGroup;
  analysisTypes: AnalysisType[] = [];
  selectedPatient: User | null = null;
  patientId: number | null = null;
  isLoading = false;
  estimatedPrice = 0;
  selectedMapLocation: SimpleLocation | null = null;

  constructor(
    private fb: FormBuilder,
    private adminService: AdminService,
    private appointmentService: AppointmentService,
    private router: Router,
    private route: ActivatedRoute,
    private snackBar: MatSnackBar,
    private cdr: ChangeDetectorRef
  ) {
    this.appointmentForm = this.fb.group({
      scheduledDate: ['', Validators.required],
      homeAddress: ['', Validators.required],
      analysisTypeIds: [[], Validators.required],
      symptoms: [''],
      specialInstructions: [''],
      isUrgent: [false]
    });

    // Calculate price when analysis types change
    this.appointmentForm.get('analysisTypeIds')?.valueChanges.subscribe(ids => {
      this.calculateEstimatedPrice(ids);
    });
  }

  ngOnInit(): void {
    // Récupérer l'ID du patient depuis les paramètres de route
    this.route.params.subscribe(params => {
      this.patientId = +params['patientId'];
      if (this.patientId) {
        this.loadPatientInfo();
      }
    });
    
    this.loadAnalysisTypes();
  }

  loadPatientInfo(): void {
    if (!this.patientId) return;

    this.adminService.getPatientById(this.patientId).subscribe({
      next: (patient) => {
        this.selectedPatient = patient;
        this.cdr.detectChanges();
      },
      error: (error) => {
        console.error('Erreur chargement patient:', error);
        this.snackBar.open('Erreur lors du chargement des informations du patient', 'Fermer', {
          duration: 5000,
          panelClass: ['error-snackbar']
        });
        this.router.navigate(['/dashboard/admin']);
      }
    });
  }

  loadAnalysisTypes(): void {
    this.appointmentService.getAnalysisTypes().subscribe({
      next: (types) => {
        this.analysisTypes = types.filter(type => type.isActive);
        this.cdr.detectChanges();
      },
      error: (error) => {
        console.error('Error loading analysis types:', error);
        this.snackBar.open('Erreur lors du chargement des types d\'analyses', 'Fermer', {
          duration: 5000,
          panelClass: ['error-snackbar']
        });
      }
    });
  }

  calculateEstimatedPrice(analysisTypeIds: number[]): void {
    if (!analysisTypeIds || analysisTypeIds.length === 0) {
      this.estimatedPrice = 0;
      return;
    }

    this.estimatedPrice = this.analysisTypes
      .filter(type => analysisTypeIds.includes(type.id))
      .reduce((total, type) => total + type.price, 0);
  }

  onLocationSelected(location: SimpleLocation): void {
    this.selectedMapLocation = location;
    if (location) {
      this.appointmentForm.patchValue({
        homeAddress: location.address
      });
    }
  }

  onSubmit(): void {
    if (this.appointmentForm.valid && this.patientId) {
      // Vérifier qu'une position a été sélectionnée
      if (!this.selectedMapLocation) {
        this.snackBar.open(
          '⚠️ Veuillez sélectionner la position sur la carte avant de continuer',
          'Fermer',
          { duration: 5000, panelClass: ['warning-snackbar'] }
        );
        return;
      }

      this.isLoading = true;

      const formValue = this.appointmentForm.value;
      const appointmentData: AppointmentCreate = {
        scheduledDate: formValue.scheduledDate,
        homeAddress: formValue.homeAddress,
        analysisTypeIds: formValue.analysisTypeIds,
        symptoms: formValue.symptoms || undefined,
        specialInstructions: formValue.specialInstructions || undefined,
        isUrgent: formValue.isUrgent || false,
        latitude: this.selectedMapLocation?.latitude,
        longitude: this.selectedMapLocation?.longitude
      };

      this.adminService.createAppointmentForPatient(this.patientId, appointmentData).subscribe({
        next: () => {
          this.isLoading = false;
          this.cdr.detectChanges();
          this.snackBar.open('Rendez-vous créé avec succès!', 'Fermer', {
            duration: 3000,
            panelClass: ['success-snackbar']
          });
          this.router.navigate(['/dashboard/admin']);
        },
        error: (error) => {
          this.isLoading = false;
          this.cdr.detectChanges();
          console.error('Error creating appointment:', error);
          this.snackBar.open('Erreur lors de la création du rendez-vous', 'Fermer', {
            duration: 5000,
            panelClass: ['error-snackbar']
          });
        }
      });
    }
  }
}
