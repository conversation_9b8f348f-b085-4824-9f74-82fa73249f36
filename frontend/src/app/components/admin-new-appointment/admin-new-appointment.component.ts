import { Component, OnInit, ChangeDetectorRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormBuilder, FormGroup, Validators, FormsModule } from '@angular/forms';
import { RouterModule, Router, ActivatedRoute } from '@angular/router';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatSelectModule } from '@angular/material/select';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { AdminService } from '../../services/admin.service';
import { AppointmentService } from '../../services/appointment.service';
import { AnalysisType, AppointmentCreate } from '../../models/appointment.model';
import { User } from '../../models/user.model';
import { SimpleLocationPickerComponent, SimpleLocation } from '../simple-location-picker/simple-location-picker.component';

@Component({
  selector: 'app-admin-new-appointment',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    FormsModule,
    RouterModule,
    MatCardModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatIconModule,
    MatSelectModule,
    MatCheckboxModule,
    MatDatepickerModule,
    MatNativeDateModule,
    MatSnackBarModule,
    SimpleLocationPickerComponent
  ],
  template: `
    <div class="admin-new-appointment-container">
      <mat-card class="appointment-card">
        <mat-card-header>
          <div class="header-content">
            <div class="title-section">
              <h2 class="card-title">
                <span class="custom-icon">👨‍💼</span>
                Nouveau rendez-vous
              </h2>

            </div>
            <button mat-icon-button routerLink="/dashboard/admin" class="close-btn">
              <mat-icon>close</mat-icon>
            </button>
          </div>
        </mat-card-header>

        <mat-card-content>
          <!-- Carte du patient -->
          <div class="patient-info-card" *ngIf="selectedPatient">
            <div class="patient-card-header">
              <h3 class="patient-card-title">
                <span class="custom-icon">👤</span>
                Informations du patient
              </h3>
            </div>
            <div class="patient-card-content">
              <div class="patient-main-info">
                <div class="patient-avatar">
                  {{ selectedPatient.firstName.charAt(0) }}{{ selectedPatient.lastName.charAt(0) }}
                </div>
                <div class="patient-details">
                  <div class="patient-name">{{ selectedPatient.firstName }} {{ selectedPatient.lastName }}</div>
                  <div class="patient-email">{{ selectedPatient.email }}</div>
                  <div class="patient-phone" *ngIf="selectedPatient.phone">{{ selectedPatient.phone }}</div>
                </div>
              </div>
              <div class="patient-additional-info">
                <div class="info-item" *ngIf="selectedPatient.address">
                  <span class="info-label">📍 Adresse :</span>
                  <span class="info-value">{{ selectedPatient.address }}</span>
                </div>
                <div class="info-item">
                  <span class="info-label">📅 Membre depuis :</span>
                  <span class="info-value">{{ selectedPatient.createdAt | date:'dd/MM/yyyy' }}</span>
                </div>
              </div>
            </div>
          </div>
          <form [formGroup]="appointmentForm" (ngSubmit)="onSubmit()" class="appointment-form">
            
            <!-- Section 1: Informations de base -->
            <div class="form-section">
              <h3 class="section-title">
                <span class="custom-icon">⏰</span>
                Date et heure du rendez-vous
              </h3>

              <mat-form-field appearance="outline" class="full-width">
                <mat-label>Date et heure souhaitées</mat-label>
                <input matInput [matDatepicker]="picker" formControlName="scheduledDate" required>
                <mat-datepicker-toggle matSuffix [for]="picker"></mat-datepicker-toggle>
                <mat-datepicker #picker></mat-datepicker>
                <mat-hint>Sélectionnez une date et heure pour le patient</mat-hint>
                <mat-error *ngIf="appointmentForm.get('scheduledDate')?.hasError('required')">
                  La date est requise
                </mat-error>
              </mat-form-field>
            </div>

            <!-- Section 2: Localisation -->
            <div class="form-section">
              <h3 class="section-title">
                <span class="custom-icon">📍</span>
                Où se déroule le rendez-vous ?
              </h3>

              <!-- Sélecteur de position sur carte -->
              <div class="location-picker-container">
                <app-simple-location-picker
                  (locationSelected)="onLocationSelected($event)"
                  [initialLocation]="selectedMapLocation">
                </app-simple-location-picker>
              </div>

              <!-- Adresse manuelle si pas de position sélectionnée -->
              <mat-form-field appearance="outline" class="full-width" *ngIf="!selectedMapLocation">
                <mat-label>Ou saisissez l'adresse manuellement</mat-label>
                <textarea matInput formControlName="homeAddress" rows="3" required
                          placeholder="Numéro, rue, ville, code postal..."></textarea>
                <span class="custom-icon" matSuffix>✏️</span>
                <mat-hint>Vous pouvez aussi utiliser la carte ci-dessus</mat-hint>
                <mat-error *ngIf="appointmentForm.get('homeAddress')?.hasError('required')">
                  L'adresse est requise
                </mat-error>
              </mat-form-field>
            </div>

            <!-- Section 3: Types d'analyses -->
            <div class="form-section">
              <h3 class="section-title">
                <span class="custom-icon">🧪</span>
                Analyses demandées
              </h3>

              <mat-form-field appearance="outline" class="full-width analysis-select">
                <mat-label>Sélectionnez les analyses</mat-label>
                <mat-select formControlName="analysisTypeIds" multiple required>
                  <mat-option *ngFor="let analysis of analysisTypes" [value]="analysis.id" class="analysis-option">
                    <div class="analysis-item">
                      <div class="analysis-main">
                        <span class="analysis-name">{{analysis.name}}</span>
                        <span class="analysis-price">{{analysis.price}}€</span>
                      </div>
                      <div class="analysis-details">
                        <span class="analysis-duration">
                          <span class="custom-icon">⏱️</span>
                          {{analysis.durationMinutes}} min
                        </span>
                        <span class="analysis-prep" *ngIf="analysis.preparationRequired">
                          <span class="custom-icon">⚠️</span>
                          Préparation requise
                        </span>
                      </div>
                    </div>
                  </mat-option>
                </mat-select>
                <mat-error *ngIf="appointmentForm.get('analysisTypeIds')?.hasError('required')">
                  Au moins une analyse est requise
                </mat-error>
              </mat-form-field>

              <!-- Prix estimé -->
              <div class="price-summary" *ngIf="estimatedPrice > 0">
                <span class="custom-icon">💰</span>
                <span class="price-label">Prix estimé :</span>
                <span class="price-value">{{ estimatedPrice }}€</span>
              </div>
            </div>

            <!-- Section 4: Informations complémentaires -->
            <div class="form-section">
              <h3 class="section-title">
                <span class="custom-icon">📝</span>
                Informations complémentaires
              </h3>

              <mat-form-field appearance="outline" class="full-width">
                <mat-label>Symptômes (optionnel)</mat-label>
                <textarea matInput formControlName="symptoms" rows="3"
                          placeholder="Décrivez les symptômes du patient..."></textarea>
                <span class="custom-icon" matSuffix>🩹</span>
                <mat-hint>Ces informations aideront notre équipe médicale</mat-hint>
              </mat-form-field>

              <mat-form-field appearance="outline" class="full-width">
                <mat-label>Instructions spéciales (optionnel)</mat-label>
                <textarea matInput formControlName="specialInstructions" rows="3"
                          placeholder="Instructions particulières pour l'infirmier..."></textarea>
                <span class="custom-icon" matSuffix>📋</span>
                <mat-hint>Informations importantes pour le prélèvement</mat-hint>
              </mat-form-field>

              <div class="checkbox-section">
                <mat-checkbox formControlName="isUrgent" class="urgent-checkbox">
                  <span class="custom-icon">🚨</span>
                  Rendez-vous urgent
                </mat-checkbox>
                <p class="checkbox-hint">
                  Cochez cette case si le rendez-vous nécessite une prise en charge prioritaire
                </p>
              </div>
            </div>

            <!-- Section 5: Affectation d'infirmier -->
            <div class="form-section">
              <h3 class="section-title">
                <span class="custom-icon">👩‍⚕️</span>
                Affectation d'infirmier (optionnel)
              </h3>

              <div class="nurse-assignment-options">
                <div class="assignment-choice">
                  <input type="radio" id="no-assignment" name="nurseAssignment" value="none"
                         [(ngModel)]="nurseAssignmentChoice" class="radio-input">
                  <label for="no-assignment" class="radio-label">
                    <span class="custom-icon">⏳</span>
                    Assigner plus tard
                  </label>
                  <p class="choice-description">Le rendez-vous sera créé en attente d'affectation</p>
                </div>

                <div class="assignment-choice">
                  <input type="radio" id="auto-assignment" name="nurseAssignment" value="auto"
                         [(ngModel)]="nurseAssignmentChoice" class="radio-input">
                  <label for="auto-assignment" class="radio-label">
                    <span class="custom-icon">🤖</span>
                    Affectation automatique
                  </label>
                  <p class="choice-description">L'infirmier le plus proche sera automatiquement assigné</p>
                </div>

                <div class="assignment-choice">
                  <input type="radio" id="manual-assignment" name="nurseAssignment" value="manual"
                         [(ngModel)]="nurseAssignmentChoice" class="radio-input">
                  <label for="manual-assignment" class="radio-label">
                    <span class="custom-icon">👥</span>
                    Choisir un infirmier
                  </label>
                  <p class="choice-description">Sélectionner manuellement un infirmier disponible</p>
                </div>
              </div>

              <!-- Sélection manuelle d'infirmier -->
              <div class="nurse-selection" *ngIf="nurseAssignmentChoice === 'manual'">
                <div class="nurse-search">
                  <input type="text"
                         [(ngModel)]="nurseSearchTerm"
                         (input)="filterNurses()"
                         placeholder="Rechercher un infirmier..."
                         class="search-input">
                  <span class="search-icon">🔍</span>
                </div>

                <div class="nurses-list" *ngIf="filteredNurses.length > 0">
                  <div *ngFor="let nurse of filteredNurses"
                       class="nurse-item"
                       [class.selected]="selectedNurse?.id === nurse.id"
                       (click)="selectNurse(nurse)">
                    <div class="nurse-avatar">
                      {{ nurse.firstName.charAt(0) }}{{ nurse.lastName.charAt(0) }}
                    </div>
                    <div class="nurse-info">
                      <div class="nurse-name">{{ nurse.firstName }} {{ nurse.lastName }}</div>
                      <div class="nurse-email">{{ nurse.email }}</div>
                      <div class="nurse-status" [class.available]="nurse.isAvailable">
                        <span class="status-indicator"></span>
                        {{ nurse.isAvailable ? 'Disponible' : 'Occupé' }}
                      </div>
                    </div>
                    <div class="nurse-actions">
                      <span class="select-indicator" *ngIf="selectedNurse?.id === nurse.id">✓</span>
                    </div>
                  </div>
                </div>

                <div class="no-nurses" *ngIf="filteredNurses.length === 0 && nurseSearchTerm">
                  <span class="custom-icon">😔</span>
                  Aucun infirmier trouvé pour "{{ nurseSearchTerm }}"
                </div>

                <div class="selected-nurse-summary" *ngIf="selectedNurse">
                  <div class="summary-header">
                    <span class="custom-icon">✅</span>
                    Infirmier sélectionné
                  </div>
                  <div class="summary-content">
                    <strong>{{ selectedNurse.firstName }} {{ selectedNurse.lastName }}</strong>
                    <span class="summary-email">{{ selectedNurse.email }}</span>
                  </div>
                </div>
              </div>
            </div>

            <!-- Actions -->
            <div class="form-actions">
              <button mat-stroked-button type="button" routerLink="/dashboard/admin" class="cancel-btn">
                <span class="custom-icon">⬅️</span>
                Retour
              </button>
              <button mat-raised-button color="primary" type="submit"
                      [disabled]="appointmentForm.invalid || isLoading" class="submit-btn">
                <span class="custom-icon" *ngIf="!isLoading">✅</span>
                <span class="custom-icon spinning" *ngIf="isLoading">⏳</span>
                <span *ngIf="!isLoading">Créer le rendez-vous</span>
                <span *ngIf="isLoading">Création en cours...</span>
              </button>
            </div>
          </form>
        </mat-card-content>
      </mat-card>
    </div>
  `,
  styles: [`
    .admin-new-appointment-container {
      padding: 20px;
      max-width: 900px;
      margin: 0 auto;
      background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
      min-height: 100vh;
    }

    .appointment-card {
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
      border-radius: 16px;
      overflow: hidden;
    }

    .header-content {
      display: flex;
      justify-content: space-between;
      align-items: center;
      width: 100%;
    }

    .title-section {
      flex: 1;
    }

    .card-title {
      margin: 0;
      font-size: 1.8rem;
      font-weight: 600;
      color: #2c3e50;
      display: flex;
      align-items: center;
      gap: 12px;
    }

    .card-subtitle {
      margin: 8px 0 0 0;
      color: #7f8c8d;
      font-size: 1rem;
    }

    .close-btn {
      color: #95a5a6;
    }

    .appointment-form {
      display: flex;
      flex-direction: column;
      gap: 32px;
    }

    .form-section {
      background: #f8f9fa;
      padding: 24px;
      border-radius: 12px;
      border-left: 4px solid #3498db;
    }

    .section-title {
      margin: 0 0 20px 0;
      font-size: 1.3rem;
      font-weight: 600;
      color: #2c3e50;
      display: flex;
      align-items: center;
      gap: 12px;
    }

    .full-width {
      width: 100%;
      margin-bottom: 16px;
    }

    .location-picker-container {
      margin-bottom: 20px;
      border-radius: 8px;
      overflow: hidden;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .analysis-select .mat-mdc-select-panel {
      max-height: 400px;
    }

    .analysis-item {
      padding: 8px 0;
      width: 100%;
    }

    .analysis-main {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 4px;
    }

    .analysis-name {
      font-weight: 500;
      color: #2c3e50;
    }

    .analysis-price {
      font-weight: 600;
      color: #27ae60;
    }

    .analysis-details {
      display: flex;
      gap: 16px;
      font-size: 0.85rem;
      color: #7f8c8d;
    }

    .analysis-duration,
    .analysis-prep {
      display: flex;
      align-items: center;
      gap: 4px;
    }

    .price-summary {
      background: #e8f5e8;
      padding: 16px;
      border-radius: 8px;
      display: flex;
      align-items: center;
      gap: 12px;
      margin-top: 16px;
    }

    .price-label {
      font-weight: 500;
      color: #2c3e50;
    }

    .price-value {
      font-size: 1.2rem;
      font-weight: 700;
      color: #27ae60;
    }

    .checkbox-section {
      margin-top: 16px;
    }

    .urgent-checkbox {
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .checkbox-hint {
      margin: 8px 0 0 32px;
      font-size: 0.9rem;
      color: #7f8c8d;
    }

    .form-actions {
      display: flex;
      gap: 16px;
      justify-content: flex-end;
      padding-top: 24px;
      border-top: 1px solid #ecf0f1;
    }

    .cancel-btn {
      min-width: 120px;
    }

    .submit-btn {
      min-width: 180px;
    }

    .custom-icon {
      font-size: 1.2em;
    }

    .spinning {
      animation: spin 1s linear infinite;
    }

    @keyframes spin {
      from { transform: rotate(0deg); }
      to { transform: rotate(360deg); }
    }

    /* Styles pour la carte du patient */
    .patient-info-card {
      background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
      border-radius: 12px;
      padding: 20px;
      margin-bottom: 24px;
      border-left: 4px solid #2196f3;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .patient-card-header {
      margin-bottom: 16px;
    }

    .patient-card-title {
      margin: 0;
      font-size: 1.2rem;
      font-weight: 600;
      color: #1976d2;
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .patient-card-content {
      display: flex;
      flex-direction: column;
      gap: 16px;
    }

    .patient-main-info {
      display: flex;
      align-items: center;
      gap: 16px;
    }

    .patient-avatar {
      width: 60px;
      height: 60px;
      border-radius: 50%;
      background: linear-gradient(135deg, #2196f3, #21cbf3);
      color: white;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: 600;
      font-size: 1.2rem;
      text-transform: uppercase;
    }

    .patient-details {
      flex: 1;
    }

    .patient-name {
      font-size: 1.3rem;
      font-weight: 600;
      color: #1976d2;
      margin-bottom: 4px;
    }

    .patient-email {
      color: #666;
      font-size: 0.95rem;
      margin-bottom: 2px;
    }

    .patient-phone {
      color: #666;
      font-size: 0.9rem;
    }

    .patient-additional-info {
      display: flex;
      flex-wrap: wrap;
      gap: 16px;
    }

    .info-item {
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 0.9rem;
    }

    .info-label {
      font-weight: 500;
      color: #555;
    }

    .info-value {
      color: #333;
    }

    /* Styles pour l'affectation d'infirmier */
    .nurse-assignment-options {
      display: flex;
      flex-direction: column;
      gap: 16px;
      margin-bottom: 20px;
    }

    .assignment-choice {
      border: 2px solid #e0e0e0;
      border-radius: 8px;
      padding: 16px;
      transition: all 0.3s ease;
      cursor: pointer;
    }

    .assignment-choice:hover {
      border-color: #2196f3;
      background-color: #f8f9fa;
    }

    .assignment-choice:has(.radio-input:checked) {
      border-color: #2196f3;
      background-color: #e3f2fd;
    }

    .radio-input {
      margin-right: 12px;
      transform: scale(1.2);
    }

    .radio-label {
      display: flex;
      align-items: center;
      gap: 8px;
      font-weight: 500;
      color: #333;
      cursor: pointer;
      margin-bottom: 8px;
    }

    .choice-description {
      margin: 0;
      color: #666;
      font-size: 0.9rem;
      margin-left: 32px;
    }

    .nurse-selection {
      margin-top: 20px;
      padding: 20px;
      background: #f8f9fa;
      border-radius: 8px;
      border: 1px solid #e0e0e0;
    }

    .nurse-search {
      position: relative;
      margin-bottom: 16px;
    }

    .search-input {
      width: 100%;
      padding: 12px 40px 12px 16px;
      border: 1px solid #ddd;
      border-radius: 6px;
      font-size: 1rem;
    }

    .search-icon {
      position: absolute;
      right: 12px;
      top: 50%;
      transform: translateY(-50%);
      color: #666;
    }

    .nurses-list {
      max-height: 300px;
      overflow-y: auto;
      border: 1px solid #e0e0e0;
      border-radius: 6px;
      background: white;
    }

    .nurse-item {
      display: flex;
      align-items: center;
      padding: 12px 16px;
      border-bottom: 1px solid #f0f0f0;
      cursor: pointer;
      transition: background-color 0.2s ease;
    }

    .nurse-item:hover {
      background-color: #f8f9fa;
    }

    .nurse-item.selected {
      background-color: #e3f2fd;
      border-left: 4px solid #2196f3;
    }

    .nurse-item:last-child {
      border-bottom: none;
    }

    .nurse-avatar {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      background: linear-gradient(135deg, #4caf50, #8bc34a);
      color: white;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: 600;
      font-size: 0.9rem;
      text-transform: uppercase;
      margin-right: 12px;
    }

    .nurse-info {
      flex: 1;
    }

    .nurse-name {
      font-weight: 500;
      color: #333;
      margin-bottom: 2px;
    }

    .nurse-email {
      font-size: 0.85rem;
      color: #666;
      margin-bottom: 4px;
    }

    .nurse-status {
      display: flex;
      align-items: center;
      gap: 6px;
      font-size: 0.8rem;
    }

    .status-indicator {
      width: 8px;
      height: 8px;
      border-radius: 50%;
      background-color: #f44336;
    }

    .nurse-status.available .status-indicator {
      background-color: #4caf50;
    }

    .nurse-actions {
      display: flex;
      align-items: center;
    }

    .select-indicator {
      color: #2196f3;
      font-size: 1.2rem;
      font-weight: bold;
    }

    .no-nurses {
      text-align: center;
      padding: 40px 20px;
      color: #666;
      font-style: italic;
    }

    .selected-nurse-summary {
      margin-top: 16px;
      padding: 16px;
      background: #e8f5e9;
      border-radius: 6px;
      border-left: 4px solid #4caf50;
    }

    .summary-header {
      display: flex;
      align-items: center;
      gap: 8px;
      font-weight: 600;
      color: #2e7d32;
      margin-bottom: 8px;
    }

    .summary-content {
      display: flex;
      flex-direction: column;
      gap: 4px;
    }

    .summary-email {
      font-size: 0.9rem;
      color: #666;
    }

    @media (max-width: 768px) {
      .admin-new-appointment-container {
        padding: 10px;
      }

      .form-section {
        padding: 16px;
      }

      .form-actions {
        flex-direction: column;
      }

      .cancel-btn,
      .submit-btn {
        width: 100%;
      }

      .patient-main-info {
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;
      }

      .patient-additional-info {
        flex-direction: column;
        gap: 8px;
      }

      .assignment-choice {
        padding: 12px;
      }

      .nurse-item {
        padding: 10px 12px;
      }
    }
  `]
})
export class AdminNewAppointmentComponent implements OnInit {
  appointmentForm: FormGroup;
  analysisTypes: AnalysisType[] = [];
  selectedPatient: User | null = null;
  patientId: number | null = null;
  isLoading = false;
  estimatedPrice = 0;
  selectedMapLocation: SimpleLocation | undefined = undefined;

  // Propriétés pour l'affectation d'infirmier
  nurseAssignmentChoice: 'none' | 'auto' | 'manual' = 'none';
  availableNurses: User[] = [];
  filteredNurses: User[] = [];
  selectedNurse: User | null = null;
  nurseSearchTerm = '';

  constructor(
    private fb: FormBuilder,
    private adminService: AdminService,
    private appointmentService: AppointmentService,
    private router: Router,
    private route: ActivatedRoute,
    private snackBar: MatSnackBar,
    private cdr: ChangeDetectorRef
  ) {
    this.appointmentForm = this.fb.group({
      scheduledDate: ['', Validators.required],
      homeAddress: ['', Validators.required],
      analysisTypeIds: [[], Validators.required],
      symptoms: [''],
      specialInstructions: [''],
      isUrgent: [false]
    });

    // Calculate price when analysis types change
    this.appointmentForm.get('analysisTypeIds')?.valueChanges.subscribe(ids => {
      this.calculateEstimatedPrice(ids);
    });
  }

  ngOnInit(): void {
    // Récupérer l'ID du patient depuis les paramètres de route
    this.route.params.subscribe(params => {
      this.patientId = +params['patientId'];
      if (this.patientId) {
        this.loadPatientInfo();
      }
    });

    this.loadAnalysisTypes();
    this.loadAvailableNurses();
  }

  loadPatientInfo(): void {
    if (!this.patientId) return;

    this.adminService.getPatientById(this.patientId).subscribe({
      next: (patient) => {
        this.selectedPatient = patient;
        this.cdr.detectChanges();
      },
      error: (error) => {
        console.error('Erreur chargement patient:', error);
        this.snackBar.open('Erreur lors du chargement des informations du patient', 'Fermer', {
          duration: 5000,
          panelClass: ['error-snackbar']
        });
        this.router.navigate(['/dashboard/admin']);
      }
    });
  }

  loadAnalysisTypes(): void {
    this.appointmentService.getAnalysisTypes().subscribe({
      next: (types) => {
        this.analysisTypes = types.filter(type => type.isActive);
        this.cdr.detectChanges();
      },
      error: (error) => {
        console.error('Error loading analysis types:', error);
        this.snackBar.open('Erreur lors du chargement des types d\'analyses', 'Fermer', {
          duration: 5000,
          panelClass: ['error-snackbar']
        });
      }
    });
  }

  calculateEstimatedPrice(analysisTypeIds: number[]): void {
    if (!analysisTypeIds || analysisTypeIds.length === 0) {
      this.estimatedPrice = 0;
      return;
    }

    this.estimatedPrice = this.analysisTypes
      .filter(type => analysisTypeIds.includes(type.id))
      .reduce((total, type) => total + type.price, 0);
  }

  onLocationSelected(location: SimpleLocation): void {
    this.selectedMapLocation = location;
    if (location) {
      this.appointmentForm.patchValue({
        homeAddress: location.address
      });
    }
  }

  onSubmit(): void {
    if (this.appointmentForm.valid && this.patientId) {
      // Vérifier qu'une position a été sélectionnée
      if (!this.selectedMapLocation) {
        this.snackBar.open(
          '⚠️ Veuillez sélectionner la position sur la carte avant de continuer',
          'Fermer',
          { duration: 5000, panelClass: ['warning-snackbar'] }
        );
        return;
      }

      this.isLoading = true;

      const formValue = this.appointmentForm.value;
      const appointmentData: AppointmentCreate = {
        scheduledDate: formValue.scheduledDate,
        homeAddress: formValue.homeAddress,
        analysisTypeIds: formValue.analysisTypeIds,
        symptoms: formValue.symptoms || undefined,
        specialInstructions: formValue.specialInstructions || undefined,
        isUrgent: formValue.isUrgent || false,
        latitude: this.selectedMapLocation?.latitude,
        longitude: this.selectedMapLocation?.longitude
      };

      this.adminService.createAppointmentForPatient(this.patientId, appointmentData).subscribe({
        next: (appointment) => {
          this.isLoading = false;
          this.cdr.detectChanges();

          // Gérer l'affectation d'infirmier si nécessaire
          if (this.nurseAssignmentChoice !== 'none') {
            this.handleNurseAssignment(appointment.id);
          }

          this.snackBar.open('Rendez-vous créé avec succès!', 'Fermer', {
            duration: 3000,
            panelClass: ['success-snackbar']
          });
          this.router.navigate(['/dashboard/admin']);
        },
        error: (error) => {
          this.isLoading = false;
          this.cdr.detectChanges();
          console.error('Error creating appointment:', error);
          this.snackBar.open('Erreur lors de la création du rendez-vous', 'Fermer', {
            duration: 5000,
            panelClass: ['error-snackbar']
          });
        }
      });
    }
  }

  loadAvailableNurses(): void {
    this.adminService.getAvailableNurses().subscribe({
      next: (nurses) => {
        this.availableNurses = nurses;
        this.filteredNurses = [...nurses];
        this.cdr.detectChanges();
      },
      error: (error) => {
        console.error('Erreur chargement infirmiers:', error);
        this.snackBar.open('Erreur lors du chargement des infirmiers', 'Fermer', {
          duration: 5000,
          panelClass: ['error-snackbar']
        });
      }
    });
  }

  filterNurses(): void {
    if (!this.nurseSearchTerm.trim()) {
      this.filteredNurses = [...this.availableNurses];
    } else {
      const searchTerm = this.nurseSearchTerm.toLowerCase();
      this.filteredNurses = this.availableNurses.filter(nurse =>
        nurse.firstName.toLowerCase().includes(searchTerm) ||
        nurse.lastName.toLowerCase().includes(searchTerm) ||
        nurse.email.toLowerCase().includes(searchTerm)
      );
    }
  }

  selectNurse(nurse: User): void {
    this.selectedNurse = nurse;
  }

  private handleNurseAssignment(appointmentId: number): void {
    if (this.nurseAssignmentChoice === 'auto') {
      // Affectation automatique
      this.adminService.autoAssignNearestNurse(appointmentId).subscribe({
        next: () => {
          this.snackBar.open('Infirmier assigné automatiquement!', 'Fermer', {
            duration: 3000,
            panelClass: ['success-snackbar']
          });
        },
        error: (error) => {
          console.error('Erreur affectation automatique:', error);
          this.snackBar.open('Erreur lors de l\'affectation automatique', 'Fermer', {
            duration: 5000,
            panelClass: ['error-snackbar']
          });
        }
      });
    } else if (this.nurseAssignmentChoice === 'manual' && this.selectedNurse) {
      // Affectation manuelle
      this.adminService.assignNurseToAppointment(appointmentId, this.selectedNurse.id!).subscribe({
        next: () => {
          this.snackBar.open(`Infirmier ${this.selectedNurse!.firstName} ${this.selectedNurse!.lastName} assigné!`, 'Fermer', {
            duration: 3000,
            panelClass: ['success-snackbar']
          });
        },
        error: (error) => {
          console.error('Erreur affectation manuelle:', error);
          this.snackBar.open('Erreur lors de l\'affectation de l\'infirmier', 'Fermer', {
            duration: 5000,
            panelClass: ['error-snackbar']
          });
        }
      });
    }
  }
}
